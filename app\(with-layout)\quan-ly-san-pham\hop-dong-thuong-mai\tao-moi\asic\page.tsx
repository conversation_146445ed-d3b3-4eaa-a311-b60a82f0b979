'use client';
import { IContract } from '@/apis/contracts/contracts.type';
import FormCreateAsic from '../../_components/Form/FormCreateAsic';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { useRouter } from 'next/navigation';
import { useCreateContract } from '@/apis/contracts/contracts.api';
import { toast } from 'react-toastify';
import { ROUTES } from '@/lib/routes';

const CreateAsic = () => {
    const router = useRouter();
    const { mutate: createContract } = useCreateContract({
        onSuccess: () => {
            toast.success('Thêm hợp đồng thành công');
            router.push(ROUTES.PRODUCT_MANAGEMENT.CONTRACTS.INDEX);
        },
        onError: () => {
            toast.error('Thêm hợp đồng thất bại');
        },
    });
    const handleSubmit = (data: IContract) => {
        const payload = convertFormValueToPayload(data, [
            'deliveryType',
            'regionDeliveryType',
            'paymentType',
            'valuePercent',
            'daysAfterSign',
            'paymentDocumentType',
            'paymentDocumentCount',
            'documentIncludedType',
            'documentQuantity',
            'deliveryWeek',
        ]);
        createContract(payload as IContract);
    };
    const handleClose = () => {
        router.back();
    };

    return <FormCreateAsic onSubmit={handleSubmit} onClose={handleClose} />;
};

export default CreateAsic;
