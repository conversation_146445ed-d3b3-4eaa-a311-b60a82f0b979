'use client';

import {
    useGetDetailCustomer,
    useUpdateCustomer,
} from '@/apis/customer/customer.api';
import {
    ICustomer,
    UpdateCustomerPayload,
    Addresses,
    Contacts,
    AssociatedInfoDtos,
    AssociatedInfos,
    CompanyDetailBankAccounts,
    AddBankAccountDtos,
    TradePartners,
} from '@/apis/customer/customer.type';
import {
    convertFormValueToPayload,
    convertPayloadToFormValue,
} from '@/utils/convert-data';

import { useParams, useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { Spinner } from 'reactstrap';

import { KEYS_TO_CUSTOMER } from '@/constants/key-convert';
import UpdateForm from '../../_components/UpdateForm';
// import { yupResolver } from '@hookform/resolvers/yup';
// import * as yup from 'yup';

// const schema = yup.object().shape({
//     name: yup.string().required('<PERSON>ui lòng nhập tên doanh nghiệp'),
//     description: yup.string(),
//     website: yup.string(),
//     facebook: yup.string(),
//     youtube: yup.string(),
//     linkedInPage: yup.string(),
//     taxCode: yup.string(),
//     annualRevenue: yup.number().default(0),
//     numberOfEmployees: yup.number().default(0),
//     industryId: yup.string(),
//     CustomerType: yup.number().default(1),
//     dateOfEstablishment: yup.string(),
//     ownerId: yup.string(),
//     // Thông tin liên kết tạm thời
//     linkedPerson1: yup.string(),
//     linkedPerson2: yup.string(),
//     linkedRole1: yup.string(),
//     linkedRole2: yup.string(),
//     // Thông tin địa chỉ tạm thời
//     country: yup.string(),
//     district: yup.string(),
//     city: yup.string(),
//     ward: yup.string(),
//     address: yup.string(),
// });

export default function EditCustomer() {
    const router = useRouter();
    const params = useParams();
    const id = params.id as string;
    const { data: customerDetail, isLoading } = useGetDetailCustomer(id);
    const { mutate: updateCustomer } = useUpdateCustomer({
        onSuccess: () => {
            toast.success('Cập nhật khách hàng thành công');
            router.back();
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });
    const handleUpdateCustomer = (data: ICustomer) => {
        const convertedData = convertFormValueToPayload(data, KEYS_TO_CUSTOMER);

        const updatePayload: UpdateCustomerPayload = {
            id: id,
            name: convertedData.name,
            shortName: convertedData.shortName || '',
            website: convertedData.website || '',
            facebook: convertedData.facebook || '',
            youtube: convertedData.youtube || '',
            linkedInPage: convertedData.linkedInPage || '',
            taxCode: convertedData.taxCode || '',
            annualRevenue: convertedData.annualRevenue || 0,
            numberOfEmployees: convertedData.numberOfEmployees || 0,
            industryId: convertedData.industryId || '',
            businessTypeId: convertedData.businessTypeId || '',
            leadStatus: convertedData.leadStatus || 0,
            lifecycleStageEnum: convertedData.lifecycleStageEnum || 0,
            formOfPurchase: convertedData.formOfPurchase || 0,
            dateOfEstablishment: convertedData.dateOfEstablishment || 0,
            description: convertedData.description || '',

            addresses: (convertedData.addresses || []).map(
                (addr: Addresses) => ({
                    id: addr.id || '',
                    addressName: addr.addressName || '',
                    provinceId: addr.provinceId || '',
                    districtId: addr.districtId || '',
                    wardId: addr.wardId || '',
                    country: addr.country || '',
                    addressType: addr.addressType || 1,
                }),
            ),

            contacts: (convertedData.contacts || []).map(
                (contact: Contacts) => ({
                    id: contact.contactId || '',
                    companyContactRole: contact.role || 0,
                }),
            ),

            associatedInfos: (
                convertedData.associatedInfoDtos ||
                convertedData.associatedInfos ||
                []
            ).map((info: AssociatedInfoDtos | AssociatedInfos) => ({
                id: 'id' in info ? info.id : undefined,
                value: info.value || '',
                associatedInfoType: info.associatedInfoType || 1,
            })),

            bankAccountDtos: (
                convertedData.companyDetailBankAccounts ||
                convertedData.addBankAccountDtos ||
                []
            ).map((bank: CompanyDetailBankAccounts | AddBankAccountDtos) => ({
                id: 'id' in bank ? bank.id : '',
                bank: bank.bank || '',
                bankBranch: bank.bankBranch || '',
                accountNumber: bank.accountNumber || '',
                accountHolderName: bank.accountHolderName || '',
                customSwiftCode: bank.customSwiftCode || '',
            })),

            companyTradePartnerDtos: (convertedData.tradePartners || []).map(
                (partner: TradePartners) => ({
                    tradePartnerId: partner.tradePartnerId || '',
                }),
            ),
        };

        updateCustomer({ payload: updatePayload, id: id });
    };
    const handleCancel = () => {
        router.back();
    };
    if (isLoading) {
        return <Spinner />;
    }
    return (
        <UpdateForm
            initValue={
                convertPayloadToFormValue(customerDetail?.data) as ICustomer
            }
            onEdit={handleUpdateCustomer}
            onCancel={handleCancel}
        />
    );
}
