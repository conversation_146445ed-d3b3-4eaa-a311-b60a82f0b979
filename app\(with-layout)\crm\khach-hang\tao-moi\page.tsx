'use client';
import { useCreateCustomer } from '@/apis/customer/customer.api';
import { ICustomer } from '@/apis/customer/customer.type';
import { KEYS_TO_CUSTOMER } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import { convertFormValueToPayload } from '@/utils/convert-data';

import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import CustomerForm from '../_components/CustomerForm';
// import { yupResolver } from '@hookform/resolvers/yup';
// import * as yup from 'yup';

// const schema = yup.object().shape({
//     name: yup.string().required('Vui lòng nhập tên doanh nghiệp'),
//     description: yup.string(),
//     website: yup.string(),
//     facebook: yup.string(),
//     youtube: yup.string(),
//     linkedInPage: yup.string(),
//     taxCode: yup.string(),
//     annualRevenue: yup.number().default(0),
//     numberOfEmployees: yup.number().default(0),
//     industryId: yup.string(),
//     CustomerType: yup.number().default(1),
//     dateOfEstablishment: yup.string(),
//     ownerId: yup.string(),
//     // Thông tin liên kết tạm thời
//     linkedPerson1: yup.string(),
//     linkedPerson2: yup.string(),
//     linkedRole1: yup.string(),
//     linkedRole2: yup.string(),
//     // Thông tin địa chỉ tạm thời
//     country: yup.string(),
//     district: yup.string(),
//     city: yup.string(),
//     ward: yup.string(),
//     address: yup.string(),
// });

export default function CreateCustomer() {
    const router = useRouter();

    const { mutate: createCustomer } = useCreateCustomer({
        onSuccess: () => {
            toast.success('Tạo mới khách hàng thành công');
            router.push(ROUTES.CRM.CUSTOMERS.INDEX);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });

    const handleCreateCustomer = (data: ICustomer) => {
        const payload = convertFormValueToPayload(data, KEYS_TO_CUSTOMER);
        createCustomer(payload as unknown as ICustomer);
    };

    const handleCancel = () => {
        console.error('Error');
    };

    return (
        <CustomerForm onSubmit={handleCreateCustomer} onCancel={handleCancel} />
    );
}
